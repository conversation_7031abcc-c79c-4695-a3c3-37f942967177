# Voice Processing API Documentation - Extracted Endpoints

## Base URL: `/`

🔐 **Authentication**
This API uses OAuth2 Password Flow.
**Token URL:** `/login`
Secure routes require a bearer token in the `Authorization` header:
`Authorization: Bearer <token>`

---

## 📚 Endpoints

### 1. POST `/login`

**Summary:** Login

**Description:** Authenticates a user using the OAuth2 password flow.

**Request Body (x-www-form-urlencoded):**

| Field      | Type   | Required | Default    |
| :--------- | :----- | :------- | :--------- |
| `username` | string | ✅       | —          |
| `password` | string | ✅       | —          |
| `client_id`| string | ✅       | —          |
| `grant_type`| string | ❌       | `"password"` |
| `scope`    | string | ❌       | `""`       |

**Responses:**
* **200 OK** — JSON with access token
* **422 Unprocessable Entity** — Validation error

**How to Call:**
Send a POST request to `/login` with your `username`, `password`, and `client_id` in the request body (x-www-form-urlencoded).

**Example (using `curl`):**

```bash
curl -X 'POST' \
  'https://tts-api.nextai.asia/v2/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&username=superadmin&password=godmod&scope=&client_id=voice'
````

Which gives the following response
```
{
  "id": "67f7479290fe4ef9d1369753",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyZGJhZWQ0YzVhNmZkNmQ3NjUwNzcwIiwiZXhwIjoxNzUxNjI5NDg1fQ.0gGkEODe558SrWqGboUFiQjlYodYSYn2d9NSc3cDt1Y",
  "token_type": "bearer",
  "username": "superadmin",
  "role": "admin",
  "tenant_id": "682dbaed4c5a6fd6d7650770",
  "tenant_label": "voice",
  "tenant_slug": "voice"
}
```
-----

Use the access token as your bearer token for further uses

### 2\. POST `/voice-clone/clone`

**Summary:** Clone Voice and also give username and user ids


```bash
curl -X 'POST' \
  'https://tts-api.nextai.asia/v2/voice-clone/clone' \
  -H 'accept: application/json' \
  -H 'Content-Type: multipart/form-data' \
  -F 'user_name=Test User' \
  -F 'user_id=123455663' \
  -F 'file=@bibek_output.wav;type=audio/wav'
```

**Response (200 OK):**
Returns the cloned voice's unique identifier and a confirmation message.

**Example:**

```json
{
  "voice_id": "68666d83c32b49f146e7592b",
  "message": "Voice 'Test User' cloned successfully",
  "user_id": "123455663",
  "voice_name": "Test User",
  "files_saved": 1,
  "minio_base_path": "voice_cloning/123455663/Test User/",
  "audio_files": [
    {
      "file_name": "voice_sample_1_ca172f10-d573-429b-8974-0d6143b1a694.wav",
      "minio_path": "voice_cloning/123455663/Test User/voice_sample_1_ca172f10-d573-429b-8974-0d6143b1a694.wav",
      "file_size": 1683898,
      "presigned_url": "https://minio.nextai.asia/voice/voice_cloning/123455663/Test%20User/voice_sample_1_ca172f10-d573-429b-8974-0d6143b1a694.wav?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minio-admin%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T114611Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=ae3f07f9333de4ed2cb75450ac137777699b1b8ab184d7ec0619ace6a3cc425c"
    }
  ]
}
```

**Possible Errors:**

  * **500:** `API key missing` - API key for voice cloning is not present in tenant config DB.
  * **500:** `An error occurred during voice cloning` - Generic error during audio upload, voice creation, or DB storage.

**How to Call:**
After successfully logging in and obtaining a bearer token, send a POST request to `/voice-clone/clone` with `user_name`, `user_id`, and one or more audio `files` (WAV/MP3) as form data. Include your bearer token in the `Authorization` header.


-----



### 3\. GET `/preset_voices`


**Example (using `curl`):**

```bash
curl -X 'GET' \
  'https://tts-api.nextai.asia/v2/preset_voices' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyZGJhZWQ0YzVhNmZkNmQ3NjUwNzcwIiwiZXhwIjoxNzUxNjMxNTEyfQ.t0vPBUhkz8eeSc5eBwKMgRKlR_xv8KbPPKsr4qhe-E4'
```

```
{
  "message": "Preset voices retrieved successfully",
  "total_voices": 6,
  "voices": [
    {
      "value": "formal",
      "label": "Formal Voice",
      "description": "Professional, formal voice suitable for business presentations",
      "file_path": "Voices/formal.mp3"
    },
    {
      "value": "guided",
      "label": "Guided Voice",
      "description": "Clear, instructional voice perfect for tutorials and guidance",
      "file_path": "Voices/guided.mp3"
    },
    {
      "value": "learning",
      "label": "Learning Voice",
      "description": "Educational voice optimized for learning content and explanations",
      "file_path": "Voices/learning.mp3"
    },
    {
      "value": "news-anchor",
      "label": "News Anchor Voice",
      "description": "Authoritative news anchor voice for broadcasting and announcements",
      "file_path": "Voices/news-anchor.mp3"
    },
    {
      "value": "podcast-host",
      "label": "Podcast Host Voice",
      "description": "Engaging podcast host voice for conversational content",
      "file_path": "Voices/podcast-host.mp3"
    },
    {
      "value": "promo",
      "label": "Promotional Voice",
      "description": "Dynamic promotional voice for marketing and advertising content",
      "file_path": "Voices/promo.wav"
    }
  ]
}
```

-----

### 4\. POST `/speak_v2`

```bash
curl -X 'POST' \
  'https://tts-api.nextai.asia/v2/speak_v2' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzdXBlcmFkbWluIiwicm9sZSI6ImFkbWluIiwidGVuYW50X2lkIjoiNjgyZGJhZWQ0YzVhNmZkNmQ3NjUwNzcwIiwiZXhwIjoxNzUxNjMxNTEyfQ.t0vPBUhkz8eeSc5eBwKMgRKlR_xv8KbPPKsr4qhe-E4' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'text=authentication%20via%20Bearer%20token.%20authentication%20via%20Bearer%20token.%20authentication%20via%20Bearer%20token.%20authentication%20via%20Bearer%20token.&preset_voice=guided&user_voice_id=685d33bb1088e080834606f3'
```

Use the voice_id you get from the /clone endpoint to use as user_voice_id
pass preset_voice as options that you can get using the /preset_voices endpoint
pass input_text as the text field


```json
{
  "message": "speak_v2 workflow completed successfully",
  "final_audio_url": "https://minio.nextai.asia/voice/speak_v2/43235432345/Chandan%20Mishra/speak_v2_output_d04e9020-d456-4cdf-8f50-9981ae5a30f6.wav?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minio-admin%2F20250703%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250703T122153Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=d65a43cd07befcc3d796b27cec3144724c4a84b478a7896b3063193fad9ca31b",
  "text": "authentication via Bearer token. authentication via Bearer token. authentication via Bearer token. authentication via Bearer token.",
  "preset_voice": "guided",
  "user_voice_id": "685d33bb1088e080834606f3",
  "user_id": "43235432345",
  "user_name": "Chandan Mishra",
  "final_file_size": 2181178,
  "created_at": "2025-07-03T12:21:53.701823",
  "minio_path": "speak_v2/43235432345/Chandan Mishra/speak_v2_output_d04e9020-d456-4cdf-8f50-9981ae5a30f6.wav",
  "workflow_steps": {
    "voice_cloning_tts_size": 728128,
    "timbre_transfer_size": 2181178
  }
}
```

Use the final_audio_url as the final audio output 
