{
  "input": {
    "content": "URL_TO_AUDIO_FILE",
    "content_type": "url"
  },
  "generation_schema": {
    "output_schema": {
      "temperature": 1,
      "top_p": 0.95,
      "top_k": 40,
      "max_output_tokens": 2048,
      "response_schema": {
        "type": "object",
        "properties": {
          // Your custom schema properties here
          "required": [
            // Required properties from your schema
          ]
        },
        "response_mime_type": "application/json"
      },
      "prompts": {
        "system": "System prompt for processing",
        "user": "User instruction for processing"
      },
      "description": "Description of the processing task"
    }
  }
}
