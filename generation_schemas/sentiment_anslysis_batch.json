{"input": {"content": "[\"yo muji ko kaam xaina\", \"ramro cha ni yo kura\", \"kasto funny manche ho yaar\"]", "content_type": "text"}, "generation_schema": {"output_schema": {"temperature": 1, "top_p": 0.95, "top_k": 40, "max_output_tokens": 2048, "response_schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"Emotion Keywords": {"type": "array", "items": {"type": "string"}}, "Sentiment": {"type": "string", "enum": ["positive", "negative", "funny"]}, "Sentiment Score": {"type": "number"}}, "required": ["Emotion Keywords", "Sentiment", "Sentiment Score"]}}}, "required": ["results"]}, "response_mime_type": "application/json"}, "prompts": {"system": "You are given a JSON array of Facebook comments in string form. Parse them, and for each comment, perform sentiment analysis. Return the result as a JSON object with a 'results' array containing the analysis for each comment.", "user": "User's Comments"}, "description": "Perform batch sentiment analysis on multiple Facebook comments encoded as a stringified JSON array."}}